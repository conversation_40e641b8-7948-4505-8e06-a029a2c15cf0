import sqlite3
import os
from contextlib import contextmanager
import time
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def get_db_path():
    app_data_path = os.path.join(os.getenv('APPDATA'), 'CRM_System')
    os.makedirs(app_data_path, exist_ok=True)
    return os.path.join(app_data_path, 'crm_database.db')

@contextmanager
def get_db_connection():
    """Context manager for database connections with retry logic"""
    max_retries = 3
    retry_delay = 1  # seconds
    
    conn = None
    for attempt in range(max_retries):
        try:
            conn = sqlite3.connect(
                get_db_path(),
                timeout=30,  # Increased timeout
                isolation_level=None  # Let us control transactions manually
            )
            # Optimize for multi-user access
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA busy_timeout=30000")  # 30 second busy timeout
            conn.execute("PRAGMA foreign_keys=ON")
            
            yield conn
            return  # Success - exit the retry loop
            
        except sqlite3.OperationalError as e:
            if "database is locked" not in str(e) or attempt == max_retries - 1:
                logger.error(f"Database error (attempt {attempt+1}): {str(e)}")
                raise
            logger.warning(f"Database locked, retrying in {retry_delay*(attempt+1)} seconds...")
            time.sleep(retry_delay * (attempt + 1))
            
        finally:
            if conn:
                conn.close()

def execute_with_retry(query, params=(), max_retries=3):
    """Execute a query with automatic retry on locked database"""
    for attempt in range(max_retries):
        try:
            with get_db_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("BEGIN")
                cursor.execute(query, params)
                conn.commit()
                return cursor.lastrowid if "INSERT" in query.upper() else cursor.rowcount
        except sqlite3.OperationalError as e:
            if "database is locked" not in str(e) or attempt == max_retries - 1:
                raise
            time.sleep(1 * (attempt + 1))
